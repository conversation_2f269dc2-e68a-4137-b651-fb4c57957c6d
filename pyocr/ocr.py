import hashlib
import json
import time
import uuid
import os
import threading
import logging
import multiprocessing
from datetime import datetime, timedelta
from concurrent.futures import Thread<PERSON>oolExecutor, ProcessPoolExecutor, as_completed
from flask import Flask, jsonify, request, send_file
from werkzeug.utils import secure_filename
from paddleocr import PaddleOCR

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(threadName)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('ocr_service.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

app = Flask(__name__)

# 配置参数
MAX_WORKERS = 4  # 最大工作进程数
UPLOAD_FOLDER = './uploads'
OUTPUT_FOLDER = './output'
ALLOWED_EXTENSIONS = {'png', 'jpg', 'jpeg', 'gif', 'bmp', 'tiff'}
MAX_FILE_SIZE = 16 * 1024 * 1024  # 16MB
TASK_TIMEOUT = 300  # 5分钟超时

# 确保目录存在
os.makedirs(UPLOAD_FOLDER, exist_ok=True)
os.makedirs(OUTPUT_FOLDER, exist_ok=True)

# 全局变量
executor = None
tasks = {}  # 任务状态存储
tasks_lock = threading.Lock()

# 线程本地存储，每个线程有自己的OCR实例
thread_local = threading.local()

# 任务状态枚举
class TaskStatus:
    PENDING = "pending"
    PROCESSING = "processing"
    COMPLETED = "completed"
    FAILED = "failed"
    TIMEOUT = "timeout"

def get_thread_ocr():
    """获取线程本地的OCR实例"""
    if not hasattr(thread_local, 'ocr'):
        logger.info(f"为线程 {threading.current_thread().name} 初始化OCR模型...")
        thread_local.ocr = PaddleOCR(
            use_doc_orientation_classify=False,
            use_doc_unwarping=False,
            use_textline_orientation=False,
            show_log=False  # 减少日志输出
        )
        logger.info(f"线程 {threading.current_thread().name} OCR模型初始化完成")
    return thread_local.ocr

def init_executor():
    """初始化执行器"""
    global executor
    # 使用进程池而不是线程池，避免GIL限制
    executor = ProcessPoolExecutor(max_workers=MAX_WORKERS)
    logger.info(f"进程池初始化完成，最大工作进程数: {MAX_WORKERS}")

def ocr_worker_process(task_id, img_path, output_folder):
    """独立进程中的OCR处理函数"""
    try:
        import time
        import json
        import os
        from paddleocr import PaddleOCR

        # 在新进程中初始化OCR
        ocr = PaddleOCR(
            use_doc_orientation_classify=False,
            use_doc_unwarping=False,
            use_textline_orientation=False,
            show_log=False
        )

        # 执行OCR识别
        start_time = time.time()
        result = ocr.predict(img_path)
        end_time = time.time()

        # 生成结果文件路径
        result_filename = f"res_{task_id}.json"
        save_path = os.path.join(output_folder, result_filename)

        # 保存结果到JSON文件
        for res in result:
            res.save_to_json(save_path=save_path)

        # 读取结果数据
        with open(save_path, "r", encoding='utf-8') as f:
            result_data = json.load(f)

        processing_time = end_time - start_time

        return {
            'success': True,
            'task_id': task_id,
            'processing_time': processing_time,
            'result_file': result_filename,
            'result_data': result_data
        }

    except Exception as e:
        return {
            'success': False,
            'task_id': task_id,
            'error': str(e)
        }

def allowed_file(filename):
    """检查文件扩展名是否允许"""
    return '.' in filename and \
           filename.rsplit('.', 1)[1].lower() in ALLOWED_EXTENSIONS

def cleanup_old_tasks():
    """清理过期任务"""
    current_time = datetime.now()
    with tasks_lock:
        expired_tasks = []
        for task_id, task_info in tasks.items():
            if current_time - task_info['created_at'] > timedelta(hours=1):
                expired_tasks.append(task_id)

        for task_id in expired_tasks:
            del tasks[task_id]
            logger.info(f"清理过期任务: {task_id}")

def process_ocr_task_async(task_id, img_path):
    """异步处理OCR任务的包装函数"""
    try:
        logger.info(f"开始处理OCR任务: {task_id} (进程模式)")

        # 更新任务状态为处理中
        with tasks_lock:
            if task_id in tasks:
                tasks[task_id]['status'] = TaskStatus.PROCESSING
                tasks[task_id]['started_at'] = datetime.now()

        # 提交到进程池执行
        future = executor.submit(ocr_worker_process, task_id, img_path, OUTPUT_FOLDER)

        # 等待结果
        result = future.result(timeout=TASK_TIMEOUT)

        if result['success']:
            logger.info(f"OCR任务 {task_id} 处理完成，耗时: {result['processing_time']:.2f}秒")

            # 更新任务状态为完成
            with tasks_lock:
                if task_id in tasks:
                    tasks[task_id].update({
                        'status': TaskStatus.COMPLETED,
                        'completed_at': datetime.now(),
                        'processing_time': result['processing_time'],
                        'result_file': result['result_file'],
                        'result_data': result['result_data']
                    })
        else:
            logger.error(f"OCR任务 {task_id} 处理失败: {result['error']}")

            # 更新任务状态为失败
            with tasks_lock:
                if task_id in tasks:
                    tasks[task_id].update({
                        'status': TaskStatus.FAILED,
                        'error': result['error'],
                        'failed_at': datetime.now()
                    })

        return result['success']

    except Exception as e:
        logger.error(f"OCR任务 {task_id} 处理异常: {str(e)}")

        # 更新任务状态为失败
        with tasks_lock:
            if task_id in tasks:
                tasks[task_id].update({
                    'status': TaskStatus.FAILED,
                    'error': str(e),
                    'failed_at': datetime.now()
                })

        return False

    finally:
        # 清理上传的临时文件
        try:
            if os.path.exists(img_path):
                os.remove(img_path)
                logger.info(f"清理临时文件: {img_path}")
        except Exception as e:
            logger.warning(f"清理临时文件失败: {str(e)}")

def process_ocr_task_thread(task_id, img_path):
    """线程模式处理OCR任务（备用方案）"""
    try:
        logger.info(f"开始处理OCR任务: {task_id} (线程模式)")

        # 更新任务状态为处理中
        with tasks_lock:
            if task_id in tasks:
                tasks[task_id]['status'] = TaskStatus.PROCESSING
                tasks[task_id]['started_at'] = datetime.now()

        # 获取线程本地的OCR实例
        ocr_instance = get_thread_ocr()

        # 执行OCR识别
        start_time = time.time()
        result = ocr_instance.predict(img_path)
        end_time = time.time()

        # 生成结果文件路径
        result_filename = f"res_{task_id}.json"
        save_path = os.path.join(OUTPUT_FOLDER, result_filename)

        # 保存结果到JSON文件
        for res in result:
            res.save_to_json(save_path=save_path)

        # 读取结果数据
        with open(save_path, "r", encoding='utf-8') as f:
            result_data = json.load(f)

        processing_time = end_time - start_time
        logger.info(f"OCR任务 {task_id} 处理完成，耗时: {processing_time:.2f}秒")

        # 更新任务状态为完成
        with tasks_lock:
            if task_id in tasks:
                tasks[task_id].update({
                    'status': TaskStatus.COMPLETED,
                    'completed_at': datetime.now(),
                    'processing_time': processing_time,
                    'result_file': result_filename,
                    'result_data': result_data
                })

        return True

    except Exception as e:
        logger.error(f"OCR任务 {task_id} 处理失败: {str(e)}")

        # 更新任务状态为失败
        with tasks_lock:
            if task_id in tasks:
                tasks[task_id].update({
                    'status': TaskStatus.FAILED,
                    'error': str(e),
                    'failed_at': datetime.now()
                })

        return False

    finally:
        # 清理上传的临时文件
        try:
            if os.path.exists(img_path):
                os.remove(img_path)
                logger.info(f"清理临时文件: {img_path}")
        except Exception as e:
            logger.warning(f"清理临时文件失败: {str(e)}")

# 初始化执行器
init_executor()

# 使用进程模式还是线程模式
USE_PROCESS_MODE = True  # 设置为True使用进程模式，False使用线程模式

# 如果使用线程模式，初始化线程池
if not USE_PROCESS_MODE:
    thread_executor = ThreadPoolExecutor(max_workers=MAX_WORKERS)
    logger.info(f"线程池初始化完成，最大工作线程数: {MAX_WORKERS}")

@app.route("/", methods=["GET"])
def health_check():
    """健康检查接口"""
    return jsonify({
        "code": 200,
        "msg": "OCR服务运行正常",
        "data": {
            "service": "OCR Multi-thread Service",
            "status": "running",
            "max_workers": MAX_WORKERS,
            "active_tasks": len([t for t in tasks.values() if t['status'] in [TaskStatus.PENDING, TaskStatus.PROCESSING]])
        }
    })

@app.route("/ocr_img", methods=["POST"])
def submit_ocr_task():
    """提交OCR任务接口"""
    try:
        # 清理过期任务
        cleanup_old_tasks()

        # 检查是否有文件上传
        if 'file' not in request.files:
            return jsonify({
                "code": 400,
                "msg": "没有上传文件",
                "data": None
            }), 400

        file = request.files['file']
        if file.filename == '':
            return jsonify({
                "code": 400,
                "msg": "没有选择文件",
                "data": None
            }), 400

        # 检查文件类型
        if not allowed_file(file.filename):
            return jsonify({
                "code": 400,
                "msg": f"不支持的文件类型，支持的格式: {', '.join(ALLOWED_EXTENSIONS)}",
                "data": None
            }), 400

        # 检查文件大小
        file.seek(0, os.SEEK_END)
        file_size = file.tell()
        file.seek(0)

        if file_size > MAX_FILE_SIZE:
            return jsonify({
                "code": 400,
                "msg": f"文件大小超过限制 ({MAX_FILE_SIZE // (1024*1024)}MB)",
                "data": None
            }), 400

        # 生成任务ID和文件名
        task_id = str(uuid.uuid4())
        filename = secure_filename(file.filename)
        file_extension = filename.rsplit('.', 1)[1].lower()
        unique_filename = f"{task_id}.{file_extension}"
        file_path = os.path.join(UPLOAD_FOLDER, unique_filename)

        # 保存上传的文件
        file.save(file_path)
        logger.info(f"文件已保存: {file_path}")

        # 创建任务记录
        with tasks_lock:
            tasks[task_id] = {
                'task_id': task_id,
                'status': TaskStatus.PENDING,
                'filename': filename,
                'file_path': file_path,
                'file_size': file_size,
                'created_at': datetime.now(),
                'started_at': None,
                'completed_at': None,
                'failed_at': None,
                'processing_time': None,
                'result_file': None,
                'result_data': None,
                'error': None
            }

        # 根据配置选择处理模式
        if USE_PROCESS_MODE:
            # 使用独立的线程来管理进程池任务，避免阻塞Flask
            import threading
            task_thread = threading.Thread(
                target=process_ocr_task_async,
                args=(task_id, file_path),
                name=f"OCR-Task-{task_id[:8]}"
            )
            task_thread.daemon = True
            task_thread.start()
        else:
            # 提交任务到线程池
            future = thread_executor.submit(process_ocr_task_thread, task_id, file_path)

        logger.info(f"OCR任务已提交: {task_id}")

        return jsonify({
            "code": 200,
            "msg": "任务提交成功",
            "data": {
                "task_id": task_id,
                "status": TaskStatus.PENDING,
                "filename": filename,
                "file_size": file_size
            }
        })

    except Exception as e:
        logger.error(f"提交OCR任务失败: {str(e)}")
        return jsonify({
            "code": 500,
            "msg": f"服务器内部错误: {str(e)}",
            "data": None
        }), 500

@app.route("/task_status/<task_id>", methods=["GET"])
def get_task_status(task_id):
    """查询任务状态接口"""
    try:
        with tasks_lock:
            if task_id not in tasks:
                return jsonify({
                    "code": 404,
                    "msg": "任务不存在",
                    "data": None
                }), 404

            task_info = tasks[task_id].copy()

        # 转换datetime对象为字符串
        for key in ['created_at', 'started_at', 'completed_at', 'failed_at']:
            if task_info.get(key):
                task_info[key] = task_info[key].isoformat()

        # 移除敏感信息
        task_info.pop('file_path', None)
        task_info.pop('result_data', None)  # 结果数据通过专门接口获取

        return jsonify({
            "code": 200,
            "msg": "查询成功",
            "data": task_info
        })

    except Exception as e:
        logger.error(f"查询任务状态失败: {str(e)}")
        return jsonify({
            "code": 500,
            "msg": f"服务器内部错误: {str(e)}",
            "data": None
        }), 500

@app.route("/task_result/<task_id>", methods=["GET"])
def get_task_result(task_id):
    """获取任务结果接口"""
    try:
        with tasks_lock:
            if task_id not in tasks:
                return jsonify({
                    "code": 404,
                    "msg": "任务不存在",
                    "data": None
                }), 404

            task_info = tasks[task_id]

            if task_info['status'] != TaskStatus.COMPLETED:
                return jsonify({
                    "code": 400,
                    "msg": f"任务尚未完成，当前状态: {task_info['status']}",
                    "data": {
                        "task_id": task_id,
                        "status": task_info['status']
                    }
                }), 400

            result_data = task_info.get('result_data')

        return jsonify({
            "code": 200,
            "msg": "获取结果成功",
            "data": {
                "task_id": task_id,
                "status": task_info['status'],
                "processing_time": task_info['processing_time'],
                "result": result_data
            }
        })

    except Exception as e:
        logger.error(f"获取任务结果失败: {str(e)}")
        return jsonify({
            "code": 500,
            "msg": f"服务器内部错误: {str(e)}",
            "data": None
        }), 500
@app.route("/tasks", methods=["GET"])
def list_tasks():
    """列出所有任务接口"""
    try:
        with tasks_lock:
            task_list = []
            for task_id, task_info in tasks.items():
                task_summary = {
                    'task_id': task_id,
                    'status': task_info['status'],
                    'filename': task_info['filename'],
                    'file_size': task_info['file_size'],
                    'created_at': task_info['created_at'].isoformat(),
                    'processing_time': task_info.get('processing_time')
                }

                if task_info.get('started_at'):
                    task_summary['started_at'] = task_info['started_at'].isoformat()
                if task_info.get('completed_at'):
                    task_summary['completed_at'] = task_info['completed_at'].isoformat()
                if task_info.get('failed_at'):
                    task_summary['failed_at'] = task_info['failed_at'].isoformat()
                if task_info.get('error'):
                    task_summary['error'] = task_info['error']

                task_list.append(task_summary)

        # 按创建时间倒序排列
        task_list.sort(key=lambda x: x['created_at'], reverse=True)

        return jsonify({
            "code": 200,
            "msg": "查询成功",
            "data": {
                "total": len(task_list),
                "tasks": task_list
            }
        })

    except Exception as e:
        logger.error(f"列出任务失败: {str(e)}")
        return jsonify({
            "code": 500,
            "msg": f"服务器内部错误: {str(e)}",
            "data": None
        }), 500

@app.route("/task_delete/<task_id>", methods=["DELETE"])
def delete_task(task_id):
    """删除任务接口"""
    try:
        with tasks_lock:
            if task_id not in tasks:
                return jsonify({
                    "code": 404,
                    "msg": "任务不存在",
                    "data": None
                }), 404

            task_info = tasks[task_id]

            # 删除结果文件
            if task_info.get('result_file'):
                result_file_path = os.path.join(OUTPUT_FOLDER, task_info['result_file'])
                try:
                    if os.path.exists(result_file_path):
                        os.remove(result_file_path)
                        logger.info(f"删除结果文件: {result_file_path}")
                except Exception as e:
                    logger.warning(f"删除结果文件失败: {str(e)}")

            # 删除任务记录
            del tasks[task_id]

        logger.info(f"任务已删除: {task_id}")

        return jsonify({
            "code": 200,
            "msg": "任务删除成功",
            "data": {"task_id": task_id}
        })

    except Exception as e:
        logger.error(f"删除任务失败: {str(e)}")
        return jsonify({
            "code": 500,
            "msg": f"服务器内部错误: {str(e)}",
            "data": None
        }), 500

@app.route("/cleanup", methods=["POST"])
def cleanup_tasks():
    """清理已完成和失败的任务"""
    try:
        cleaned_count = 0
        with tasks_lock:
            tasks_to_delete = []
            for task_id, task_info in tasks.items():
                if task_info['status'] in [TaskStatus.COMPLETED, TaskStatus.FAILED]:
                    # 删除结果文件
                    if task_info.get('result_file'):
                        result_file_path = os.path.join(OUTPUT_FOLDER, task_info['result_file'])
                        try:
                            if os.path.exists(result_file_path):
                                os.remove(result_file_path)
                        except Exception as e:
                            logger.warning(f"删除结果文件失败: {str(e)}")

                    tasks_to_delete.append(task_id)

            for task_id in tasks_to_delete:
                del tasks[task_id]
                cleaned_count += 1

        logger.info(f"清理了 {cleaned_count} 个任务")

        return jsonify({
            "code": 200,
            "msg": f"清理完成，删除了 {cleaned_count} 个任务",
            "data": {"cleaned_count": cleaned_count}
        })

    except Exception as e:
        logger.error(f"清理任务失败: {str(e)}")
        return jsonify({
            "code": 500,
            "msg": f"服务器内部错误: {str(e)}",
            "data": None
        }), 500

@app.errorhandler(413)
def request_entity_too_large(error):
    """文件过大错误处理"""
    return jsonify({
        "code": 413,
        "msg": f"文件大小超过限制 ({MAX_FILE_SIZE // (1024*1024)}MB)",
        "data": None
    }), 413

@app.errorhandler(404)
def not_found(error):
    """404错误处理"""
    return jsonify({
        "code": 404,
        "msg": "接口不存在",
        "data": None
    }), 404

@app.errorhandler(500)
def internal_error(error):
    """500错误处理"""
    return jsonify({
        "code": 500,
        "msg": "服务器内部错误",
        "data": None
    }), 500

if __name__ == "__main__":
    # 确保在主进程中运行
    multiprocessing.set_start_method('spawn', force=True)

    try:
        logger.info("启动OCR多线程服务...")
        logger.info(f"处理模式: {'进程池' if USE_PROCESS_MODE else '线程池'}")
        logger.info(f"最大工作进程/线程数: {MAX_WORKERS}")
        logger.info(f"上传目录: {UPLOAD_FOLDER}")
        logger.info(f"输出目录: {OUTPUT_FOLDER}")
        logger.info(f"支持的文件格式: {', '.join(ALLOWED_EXTENSIONS)}")
        logger.info(f"最大文件大小: {MAX_FILE_SIZE // (1024*1024)}MB")

        app.run(host="0.0.0.0", port=5001, debug=True, threaded=True)
    except KeyboardInterrupt:
        logger.info("服务被用户中断")
    except Exception as e:
        logger.error(f"服务启动失败: {str(e)}")
    finally:
        if executor:
            logger.info("正在关闭执行器...")
            executor.shutdown(wait=True)
            logger.info("执行器已关闭")
        if not USE_PROCESS_MODE and 'thread_executor' in globals():
            logger.info("正在关闭线程池...")
            thread_executor.shutdown(wait=True)
            logger.info("线程池已关闭")
