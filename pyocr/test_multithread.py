#!/usr/bin/env python3
"""
OCR多线程服务测试脚本
测试多个并发请求的处理能力
"""

import requests
import time
import threading
import json
from concurrent.futures import ThreadPoolExecutor, as_completed

# 服务配置
OCR_SERVICE_URL = "http://localhost:5001"
TEST_IMAGE_PATH = "/Volumes/APP/test/3624774a98241be10699f1092dc759.PNG"  # 请确保这个文件存在

def test_health_check():
    """测试健康检查接口"""
    print("=== 测试健康检查接口 ===")
    try:
        response = requests.get(f"{OCR_SERVICE_URL}/")
        print(f"状态码: {response.status_code}")
        print(f"响应: {json.dumps(response.json(), indent=2, ensure_ascii=False)}")
        return response.status_code == 200
    except Exception as e:
        print(f"健康检查失败: {str(e)}")
        return False

def submit_ocr_task(task_name, image_path):
    """提交OCR任务"""
    try:
        print(f"[{task_name}] 开始提交任务...")
        
        with open(image_path, 'rb') as f:
            files = {'file': f}
            response = requests.post(f"{OCR_SERVICE_URL}/ocr_img", files=files)
        
        if response.status_code == 200:
            result = response.json()
            task_id = result['data']['task_id']
            print(f"[{task_name}] 任务提交成功，任务ID: {task_id}")
            return task_id
        else:
            print(f"[{task_name}] 任务提交失败: {response.text}")
            return None
            
    except Exception as e:
        print(f"[{task_name}] 提交任务异常: {str(e)}")
        return None

def check_task_status(task_name, task_id):
    """检查任务状态"""
    try:
        response = requests.get(f"{OCR_SERVICE_URL}/task_status/{task_id}")
        if response.status_code == 200:
            result = response.json()
            status = result['data']['status']
            print(f"[{task_name}] 任务状态: {status}")
            return status
        else:
            print(f"[{task_name}] 查询状态失败: {response.text}")
            return None
    except Exception as e:
        print(f"[{task_name}] 查询状态异常: {str(e)}")
        return None

def get_task_result(task_name, task_id):
    """获取任务结果"""
    try:
        response = requests.get(f"{OCR_SERVICE_URL}/task_result/{task_id}")
        if response.status_code == 200:
            result = response.json()
            processing_time = result['data']['processing_time']
            text_count = len(result['data']['result']['rec_texts'])
            print(f"[{task_name}] 任务完成，处理时间: {processing_time:.2f}秒，识别文本数: {text_count}")
            return result['data']
        else:
            print(f"[{task_name}] 获取结果失败: {response.text}")
            return None
    except Exception as e:
        print(f"[{task_name}] 获取结果异常: {str(e)}")
        return None

def run_single_task(task_name, image_path):
    """运行单个OCR任务的完整流程"""
    start_time = time.time()
    
    # 提交任务
    task_id = submit_ocr_task(task_name, image_path)
    if not task_id:
        return None
    
    # 轮询任务状态直到完成
    while True:
        status = check_task_status(task_name, task_id)
        if status == "completed":
            break
        elif status == "failed":
            print(f"[{task_name}] 任务失败")
            return None
        elif status in ["pending", "processing"]:
            time.sleep(1)  # 等待1秒后再次检查
        else:
            print(f"[{task_name}] 未知状态: {status}")
            return None
    
    # 获取结果
    result = get_task_result(task_name, task_id)
    
    total_time = time.time() - start_time
    print(f"[{task_name}] 总耗时: {total_time:.2f}秒")
    
    return {
        'task_name': task_name,
        'task_id': task_id,
        'total_time': total_time,
        'processing_time': result['processing_time'] if result else None,
        'success': result is not None
    }

def test_concurrent_requests(num_tasks=5):
    """测试并发请求"""
    print(f"\n=== 测试 {num_tasks} 个并发OCR任务 ===")

    # 检查测试图片是否存在
    import os
    if not os.path.exists(TEST_IMAGE_PATH):
        print(f"测试图片不存在: {TEST_IMAGE_PATH}")
        print("请修改 TEST_IMAGE_PATH 变量指向一个有效的图片文件")
        return

    # 记录开始时间
    overall_start_time = time.time()

    # 第一步：并发提交所有任务
    print("第一步：并发提交所有任务...")
    submit_start_time = time.time()
    task_ids = []

    with ThreadPoolExecutor(max_workers=num_tasks) as submit_executor:
        submit_futures = []
        for i in range(num_tasks):
            task_name = f"Task-{i+1}"
            future = submit_executor.submit(submit_ocr_task, task_name, TEST_IMAGE_PATH)
            submit_futures.append((future, task_name))

        # 收集所有任务ID
        for future, task_name in submit_futures:
            task_id = future.result()
            if task_id:
                task_ids.append((task_id, task_name))
                print(f"{task_name} 提交成功: {task_id[:8]}...")

    submit_time = time.time() - submit_start_time
    print(f"所有任务提交完成，耗时: {submit_time:.2f}秒")

    if not task_ids:
        print("没有成功提交的任务")
        return

    # 第二步：并发监控所有任务状态
    print(f"\n第二步：监控 {len(task_ids)} 个任务的执行状态...")
    monitor_start_time = time.time()
    results = []

    def monitor_task(task_info):
        task_id, task_name = task_info
        start_time = time.time()

        # 轮询任务状态直到完成
        while True:
            status = check_task_status(task_name, task_id)
            if status == "completed":
                result = get_task_result(task_name, task_id)
                total_time = time.time() - start_time
                return {
                    'task_name': task_name,
                    'task_id': task_id,
                    'total_time': total_time,
                    'processing_time': result['processing_time'] if result else None,
                    'success': result is not None
                }
            elif status == "failed":
                total_time = time.time() - start_time
                print(f"[{task_name}] 任务失败")
                return {
                    'task_name': task_name,
                    'task_id': task_id,
                    'total_time': total_time,
                    'processing_time': None,
                    'success': False
                }
            elif status in ["pending", "processing"]:
                time.sleep(0.5)  # 更频繁的检查
            else:
                print(f"[{task_name}] 未知状态: {status}")
                return None

    # 并发监控所有任务
    with ThreadPoolExecutor(max_workers=num_tasks) as monitor_executor:
        monitor_futures = []
        for task_info in task_ids:
            future = monitor_executor.submit(monitor_task, task_info)
            monitor_futures.append(future)

        # 收集结果
        for future in as_completed(monitor_futures):
            result = future.result()
            if result:
                results.append(result)

    monitor_time = time.time() - monitor_start_time
    overall_time = time.time() - overall_start_time

    # 统计结果
    successful_tasks = [r for r in results if r['success']]
    failed_tasks = [r for r in results if not r['success']]

    print(f"\n=== 并发性能测试结果 ===")
    print(f"总任务数: {num_tasks}")
    print(f"成功任务数: {len(successful_tasks)}")
    print(f"失败任务数: {len(failed_tasks)}")
    print(f"任务提交耗时: {submit_time:.2f}秒")
    print(f"任务监控耗时: {monitor_time:.2f}秒")
    print(f"总体耗时: {overall_time:.2f}秒")

    if successful_tasks:
        # 计算并发效率
        total_processing_time = sum(r['processing_time'] for r in successful_tasks)
        avg_processing_time = total_processing_time / len(successful_tasks)

        print(f"\n=== 性能分析 ===")
        print(f"单个任务平均处理时间: {avg_processing_time:.2f}秒")
        print(f"所有任务累计处理时间: {total_processing_time:.2f}秒")
        print(f"实际总耗时: {overall_time:.2f}秒")

        # 并发效率 = 累计处理时间 / 实际总耗时
        concurrency_efficiency = total_processing_time / overall_time
        print(f"并发效率: {concurrency_efficiency:.2f}x")

        if concurrency_efficiency > 1.5:
            print("✅ 并发效果良好！任务确实在并行处理")
        elif concurrency_efficiency > 1.0:
            print("⚠️  有一定并发效果，但可能存在瓶颈")
        else:
            print("❌ 并发效果不佳，任务可能在串行执行")

        # 显示每个任务的详细信息
        print(f"\n=== 任务详情 ===")
        for result in sorted(successful_tasks, key=lambda x: x['task_name']):
            print(f"{result['task_name']}: 处理耗时 {result['processing_time']:.2f}s")

    return {
        'total_tasks': num_tasks,
        'successful_tasks': len(successful_tasks),
        'failed_tasks': len(failed_tasks),
        'overall_time': overall_time,
        'concurrency_efficiency': concurrency_efficiency if successful_tasks else 0
    }

def test_list_tasks():
    """测试任务列表接口"""
    print(f"\n=== 测试任务列表接口 ===")
    try:
        response = requests.get(f"{OCR_SERVICE_URL}/tasks")
        if response.status_code == 200:
            result = response.json()
            print(f"任务总数: {result['data']['total']}")
            for task in result['data']['tasks'][:5]:  # 只显示前5个任务
                print(f"任务ID: {task['task_id'][:8]}..., 状态: {task['status']}, 文件: {task['filename']}")
        else:
            print(f"获取任务列表失败: {response.text}")
    except Exception as e:
        print(f"获取任务列表异常: {str(e)}")

def main():
    """主测试函数"""
    print("OCR多线程服务并发性能测试")
    print("=" * 60)

    # 1. 健康检查
    if not test_health_check():
        print("服务不可用，请确保OCR服务正在运行")
        return

    # 2. 测试不同并发数量
    test_cases = [3, 4, 5]  # 测试3、4、5个并发任务

    for num_tasks in test_cases:
        print(f"\n{'='*60}")
        print(f"测试 {num_tasks} 个并发任务")
        print(f"{'='*60}")

        result = test_concurrent_requests(num_tasks=num_tasks)

        if result:
            print(f"\n📊 测试 {num_tasks} 个任务的结果:")
            print(f"   - 成功率: {result['successful_tasks']}/{result['total_tasks']} ({result['successful_tasks']/result['total_tasks']*100:.1f}%)")
            print(f"   - 总耗时: {result['overall_time']:.2f}秒")
            print(f"   - 并发效率: {result['concurrency_efficiency']:.2f}x")

        # 等待一下再进行下一轮测试
        time.sleep(2)

    # 3. 查看任务列表
    test_list_tasks()

    print(f"\n{'='*60}")
    print("🎉 所有测试完成!")
    print("如果并发效率 > 1.5x，说明多线程/多进程并发工作正常")
    print("如果并发效率 ≈ 1.0x，说明任务在串行执行，需要优化")
    print(f"{'='*60}")

if __name__ == "__main__":
    main()
