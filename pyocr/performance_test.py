#!/usr/bin/env python3
"""
OCR服务性能对比测试
对比串行处理和并行处理的性能差异
"""

import requests
import time
import json
from concurrent.futures import ThreadPoolExecutor, as_completed

# 服务配置
OCR_SERVICE_URL = "http://localhost:5000"
TEST_IMAGE_PATH = "/Volumes/APP/test/3624774a98241be10699f1092dc759.PNG"  # 请修改为实际路径

def submit_and_wait_task(task_name, image_path):
    """提交任务并等待完成"""
    try:
        # 提交任务
        with open(image_path, 'rb') as f:
            files = {'file': f}
            response = requests.post(f"{OCR_SERVICE_URL}/ocr_img", files=files)
        
        if response.status_code != 200:
            print(f"[{task_name}] 任务提交失败: {response.text}")
            return None
        
        task_id = response.json()['data']['task_id']
        print(f"[{task_name}] 任务提交成功: {task_id[:8]}...")
        
        # 等待任务完成
        start_time = time.time()
        while True:
            status_response = requests.get(f"{OCR_SERVICE_URL}/task_status/{task_id}")
            if status_response.status_code != 200:
                print(f"[{task_name}] 查询状态失败")
                return None
            
            status = status_response.json()['data']['status']
            
            if status == "completed":
                # 获取结果
                result_response = requests.get(f"{OCR_SERVICE_URL}/task_result/{task_id}")
                if result_response.status_code == 200:
                    result_data = result_response.json()['data']
                    total_time = time.time() - start_time
                    return {
                        'task_name': task_name,
                        'total_time': total_time,
                        'processing_time': result_data['processing_time'],
                        'success': True
                    }
                else:
                    print(f"[{task_name}] 获取结果失败")
                    return None
            elif status == "failed":
                print(f"[{task_name}] 任务失败")
                return None
            elif status in ["pending", "processing"]:
                time.sleep(0.5)
            else:
                print(f"[{task_name}] 未知状态: {status}")
                return None
                
    except Exception as e:
        print(f"[{task_name}] 异常: {str(e)}")
        return None

def test_serial_processing(num_tasks=3):
    """测试串行处理"""
    print(f"\n=== 串行处理测试 ({num_tasks} 个任务) ===")
    
    start_time = time.time()
    results = []
    
    for i in range(num_tasks):
        task_name = f"Serial-Task-{i+1}"
        result = submit_and_wait_task(task_name, TEST_IMAGE_PATH)
        if result:
            results.append(result)
    
    total_time = time.time() - start_time
    
    if results:
        avg_processing_time = sum(r['processing_time'] for r in results) / len(results)
        total_processing_time = sum(r['processing_time'] for r in results)
        
        print(f"串行处理结果:")
        print(f"  - 成功任务数: {len(results)}/{num_tasks}")
        print(f"  - 总耗时: {total_time:.2f}秒")
        print(f"  - 平均处理时间: {avg_processing_time:.2f}秒")
        print(f"  - 累计处理时间: {total_processing_time:.2f}秒")
        
        for result in results:
            print(f"  - {result['task_name']}: {result['processing_time']:.2f}秒")
    
    return {
        'mode': 'serial',
        'num_tasks': num_tasks,
        'successful_tasks': len(results),
        'total_time': total_time,
        'results': results
    }

def test_parallel_processing(num_tasks=3):
    """测试并行处理"""
    print(f"\n=== 并行处理测试 ({num_tasks} 个任务) ===")
    
    start_time = time.time()
    results = []
    
    # 使用线程池并发提交和等待任务
    with ThreadPoolExecutor(max_workers=num_tasks) as executor:
        futures = []
        for i in range(num_tasks):
            task_name = f"Parallel-Task-{i+1}"
            future = executor.submit(submit_and_wait_task, task_name, TEST_IMAGE_PATH)
            futures.append(future)
        
        # 等待所有任务完成
        for future in as_completed(futures):
            result = future.result()
            if result:
                results.append(result)
    
    total_time = time.time() - start_time
    
    if results:
        avg_processing_time = sum(r['processing_time'] for r in results) / len(results)
        total_processing_time = sum(r['processing_time'] for r in results)
        
        print(f"并行处理结果:")
        print(f"  - 成功任务数: {len(results)}/{num_tasks}")
        print(f"  - 总耗时: {total_time:.2f}秒")
        print(f"  - 平均处理时间: {avg_processing_time:.2f}秒")
        print(f"  - 累计处理时间: {total_processing_time:.2f}秒")
        print(f"  - 并发效率: {total_processing_time/total_time:.2f}x")
        
        for result in sorted(results, key=lambda x: x['task_name']):
            print(f"  - {result['task_name']}: {result['processing_time']:.2f}秒")
    
    return {
        'mode': 'parallel',
        'num_tasks': num_tasks,
        'successful_tasks': len(results),
        'total_time': total_time,
        'results': results
    }

def compare_performance(num_tasks=3):
    """对比串行和并行处理性能"""
    print(f"\n{'='*80}")
    print(f"性能对比测试 - {num_tasks} 个任务")
    print(f"{'='*80}")
    
    # 检查测试图片
    import os
    if not os.path.exists(TEST_IMAGE_PATH):
        print(f"测试图片不存在: {TEST_IMAGE_PATH}")
        print("请修改 TEST_IMAGE_PATH 变量指向一个有效的图片文件")
        return
    
    # 健康检查
    try:
        response = requests.get(f"{OCR_SERVICE_URL}/")
        if response.status_code != 200:
            print("OCR服务不可用")
            return
        print("✅ OCR服务运行正常")
    except Exception as e:
        print(f"❌ 无法连接到OCR服务: {str(e)}")
        return
    
    # 串行处理测试
    serial_result = test_serial_processing(num_tasks)
    
    # 等待一下
    print("\n等待3秒后开始并行测试...")
    time.sleep(3)
    
    # 并行处理测试
    parallel_result = test_parallel_processing(num_tasks)
    
    # 性能对比
    if serial_result['successful_tasks'] > 0 and parallel_result['successful_tasks'] > 0:
        print(f"\n{'='*80}")
        print("📊 性能对比结果")
        print(f"{'='*80}")
        
        serial_time = serial_result['total_time']
        parallel_time = parallel_result['total_time']
        speedup = serial_time / parallel_time
        
        print(f"串行处理总耗时: {serial_time:.2f}秒")
        print(f"并行处理总耗时: {parallel_time:.2f}秒")
        print(f"性能提升倍数: {speedup:.2f}x")
        print(f"时间节省: {serial_time - parallel_time:.2f}秒 ({(1-parallel_time/serial_time)*100:.1f}%)")
        
        if speedup > 2.0:
            print("🚀 并行处理效果优秀！")
        elif speedup > 1.5:
            print("✅ 并行处理效果良好")
        elif speedup > 1.1:
            print("⚠️  并行处理有一定效果")
        else:
            print("❌ 并行处理效果不明显，可能存在瓶颈")
        
        # 计算理论最大加速比
        serial_processing_times = [r['processing_time'] for r in serial_result['results']]
        parallel_processing_times = [r['processing_time'] for r in parallel_result['results']]
        
        avg_serial_processing = sum(serial_processing_times) / len(serial_processing_times)
        avg_parallel_processing = sum(parallel_processing_times) / len(parallel_processing_times)
        
        print(f"\n📈 详细分析:")
        print(f"平均单任务处理时间 (串行): {avg_serial_processing:.2f}秒")
        print(f"平均单任务处理时间 (并行): {avg_parallel_processing:.2f}秒")
        print(f"理论最大加速比: {num_tasks:.1f}x (完美并行)")
        print(f"实际加速比: {speedup:.2f}x")
        print(f"并行效率: {speedup/num_tasks*100:.1f}%")

def main():
    """主函数"""
    print("OCR服务性能对比测试工具")
    print("对比串行处理和并行处理的性能差异")
    
    # 测试不同任务数量
    test_cases = [3, 4]
    
    for num_tasks in test_cases:
        compare_performance(num_tasks)
        if num_tasks < max(test_cases):
            print(f"\n等待5秒后进行下一轮测试...")
            time.sleep(5)
    
    print(f"\n{'='*80}")
    print("🎉 所有性能测试完成！")
    print("如果并行处理的加速比接近任务数量，说明并发效果很好")
    print("如果加速比远小于任务数量，可能需要优化并发实现")
    print(f"{'='*80}")

if __name__ == "__main__":
    main()
