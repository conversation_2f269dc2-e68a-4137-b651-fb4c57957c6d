#!/usr/bin/env python3
"""
快速测试OCR并发性能
"""

import requests
import time
import json
import os
from concurrent.futures import ThreadPoolExecutor, as_completed

# 配置
OCR_SERVICE_URL = "http://localhost:5000"
TEST_IMAGE_PATH = "/Volumes/APP/test/3624774a98241be10699f1092dc759.PNG"

def quick_test():
    """快速测试并发性能"""
    print("🚀 OCR并发性能快速测试")
    print("=" * 50)
    
    # 检查服务
    try:
        response = requests.get(f"{OCR_SERVICE_URL}/", timeout=5)
        if response.status_code == 200:
            print("✅ OCR服务连接正常")
        else:
            print("❌ OCR服务响应异常")
            return
    except Exception as e:
        print(f"❌ 无法连接OCR服务: {str(e)}")
        print("请确保OCR服务正在运行: python ocr.py")
        return
    
    # 检查测试图片
    if not os.path.exists(TEST_IMAGE_PATH):
        print(f"❌ 测试图片不存在: {TEST_IMAGE_PATH}")
        print("请修改 TEST_IMAGE_PATH 变量指向一个有效的图片文件")
        return
    
    print(f"✅ 测试图片存在: {TEST_IMAGE_PATH}")
    
    # 测试3个并发任务
    num_tasks = 3
    print(f"\n📊 开始测试 {num_tasks} 个并发任务...")
    
    def submit_task(task_id):
        """提交单个任务"""
        try:
            with open(TEST_IMAGE_PATH, 'rb') as f:
                files = {'file': f}
                response = requests.post(f"{OCR_SERVICE_URL}/ocr_img", files=files)
            
            if response.status_code == 200:
                task_data = response.json()['data']
                return task_data['task_id'], f"Task-{task_id}"
            else:
                print(f"Task-{task_id} 提交失败: {response.text}")
                return None, f"Task-{task_id}"
        except Exception as e:
            print(f"Task-{task_id} 提交异常: {str(e)}")
            return None, f"Task-{task_id}"
    
    # 并发提交任务
    start_time = time.time()
    task_ids = []
    
    print("第1步: 并发提交任务...")
    with ThreadPoolExecutor(max_workers=num_tasks) as executor:
        futures = [executor.submit(submit_task, i+1) for i in range(num_tasks)]
        for future in as_completed(futures):
            task_id, task_name = future.result()
            if task_id:
                task_ids.append((task_id, task_name))
                print(f"  ✅ {task_name} 提交成功: {task_id[:8]}...")
    
    if not task_ids:
        print("❌ 没有成功提交的任务")
        return
    
    submit_time = time.time() - start_time
    print(f"任务提交完成，耗时: {submit_time:.2f}秒")
    
    # 监控任务状态
    print(f"\n第2步: 监控 {len(task_ids)} 个任务执行...")
    
    def monitor_task(task_info):
        """监控单个任务"""
        task_id, task_name = task_info
        start_time = time.time()
        
        while True:
            try:
                response = requests.get(f"{OCR_SERVICE_URL}/task_status/{task_id}")
                if response.status_code != 200:
                    return None
                
                status = response.json()['data']['status']
                
                if status == "completed":
                    # 获取结果
                    result_response = requests.get(f"{OCR_SERVICE_URL}/task_result/{task_id}")
                    if result_response.status_code == 200:
                        result_data = result_response.json()['data']
                        total_time = time.time() - start_time
                        return {
                            'task_name': task_name,
                            'processing_time': result_data['processing_time'],
                            'total_time': total_time,
                            'success': True
                        }
                elif status == "failed":
                    print(f"  ❌ {task_name} 执行失败")
                    return {'task_name': task_name, 'success': False}
                elif status in ["pending", "processing"]:
                    time.sleep(0.5)
                else:
                    print(f"  ⚠️ {task_name} 未知状态: {status}")
                    return None
                    
            except Exception as e:
                print(f"  ❌ {task_name} 监控异常: {str(e)}")
                return None
    
    # 并发监控任务
    results = []
    with ThreadPoolExecutor(max_workers=len(task_ids)) as executor:
        futures = [executor.submit(monitor_task, task_info) for task_info in task_ids]
        for future in as_completed(futures):
            result = future.result()
            if result and result.get('success'):
                results.append(result)
                print(f"  ✅ {result['task_name']} 完成: {result['processing_time']:.2f}秒")
    
    total_time = time.time() - start_time
    
    # 分析结果
    print(f"\n📈 测试结果分析:")
    print(f"  总任务数: {num_tasks}")
    print(f"  成功任务数: {len(results)}")
    print(f"  总耗时: {total_time:.2f}秒")
    
    if results:
        processing_times = [r['processing_time'] for r in results]
        avg_processing_time = sum(processing_times) / len(processing_times)
        total_processing_time = sum(processing_times)
        
        print(f"  平均处理时间: {avg_processing_time:.2f}秒")
        print(f"  累计处理时间: {total_processing_time:.2f}秒")
        
        # 计算并发效率
        concurrency_efficiency = total_processing_time / total_time
        print(f"  并发效率: {concurrency_efficiency:.2f}x")
        
        # 评估结果
        print(f"\n🎯 性能评估:")
        if concurrency_efficiency > 2.0:
            print("  🚀 优秀！并发效果非常好")
        elif concurrency_efficiency > 1.5:
            print("  ✅ 良好！并发效果明显")
        elif concurrency_efficiency > 1.0:
            print("  ⚠️  一般，有一定并发效果")
        else:
            print("  ❌ 较差，可能在串行执行")
        
        # 理论分析
        expected_serial_time = total_processing_time
        speedup = expected_serial_time / total_time
        print(f"  理论串行耗时: {expected_serial_time:.2f}秒")
        print(f"  实际加速比: {speedup:.2f}x")
        print(f"  并行效率: {speedup/num_tasks*100:.1f}%")
        
        if speedup >= num_tasks * 0.7:
            print("  💯 并行效率很高，接近理论最优")
        elif speedup >= num_tasks * 0.5:
            print("  👍 并行效率不错")
        else:
            print("  🔧 并行效率有待提升")

def main():
    """主函数"""
    quick_test()
    
    print(f"\n{'='*50}")
    print("💡 提示:")
    print("- 如果并发效率 > 1.5x，说明多进程并发工作正常")
    print("- 如果并发效率 ≈ 1.0x，说明任务在串行执行")
    print("- 可以修改 ocr.py 中的 USE_PROCESS_MODE 来切换处理模式")
    print("- 可以调整 MAX_WORKERS 来优化并发数量")
    print(f"{'='*50}")

if __name__ == "__main__":
    main()
