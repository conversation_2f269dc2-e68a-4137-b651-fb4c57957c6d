#!/usr/bin/env python3
"""
OCR多线程服务启动脚本
支持不同的启动模式和配置
"""

import os
import sys
import argparse
import logging
from ocr import app, logger,  MAX_WORKERS

def setup_logging(log_level="INFO", log_file=None):
    """设置日志配置"""
    level = getattr(logging, log_level.upper(), logging.INFO)
    
    handlers = [logging.StreamHandler()]
    if log_file:
        handlers.append(logging.FileHandler(log_file))
    
    logging.basicConfig(
        level=level,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=handlers
    )

def check_dependencies():
    """检查依赖项"""
    try:
        import flask
        import paddleocr
        import werkzeug
        logger.info("所有依赖项检查通过")
        return True
    except ImportError as e:
        logger.error(f"缺少依赖项: {str(e)}")
        logger.error("请运行: pip install flask paddleocr werkzeug")
        return False

def create_directories():
    """创建必要的目录"""
    directories = ['./uploads', './output', './logs']
    for directory in directories:
        os.makedirs(directory, exist_ok=True)
        logger.info(f"确保目录存在: {directory}")

def start_development_server(host="0.0.0.0", port=5001, debug=False):
    """启动开发服务器"""
    logger.info("启动开发服务器模式")
    logger.info(f"服务地址: http://{host}:{port}")
    logger.info(f"调试模式: {debug}")
    
    app.run(host=host, port=port, debug=debug, threaded=True)

def start_production_server(host="0.0.0.0", port=5001, workers=None):
    """启动生产服务器 (使用gunicorn)"""
    try:
        import gunicorn.app.wsgiapp as wsgi
        
        logger.info("启动生产服务器模式 (Gunicorn)")
        logger.info(f"服务地址: http://{host}:{port}")
        logger.info(f"工作进程数: {workers or MAX_WORKERS}")
        
        # 设置gunicorn参数
        sys.argv = [
            'gunicorn',
            '--bind', f'{host}:{port}',
            '--workers', str(workers or MAX_WORKERS),
            '--worker-class', 'sync',
            '--timeout', '300',
            '--keep-alive', '2',
            '--max-requests', '1000',
            '--max-requests-jitter', '100',
            '--log-level', 'info',
            'ocr:app'
        ]
        
        wsgi.run()
        
    except ImportError:
        logger.error("Gunicorn未安装，请运行: pip install gunicorn")
        logger.info("回退到开发服务器模式")
        start_development_server(host, port, debug=False)

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='OCR多线程服务启动器')
    parser.add_argument('--host', default='0.0.0.0', help='服务器地址 (默认: 0.0.0.0)')
    parser.add_argument('--port', type=int, default=5001, help='服务器端口 (默认: 5000)')
    parser.add_argument('--mode', choices=['dev', 'prod'], default='dev', 
                       help='启动模式: dev=开发模式, prod=生产模式 (默认: dev)')
    parser.add_argument('--debug', action='store_true', help='启用调试模式 (仅开发模式)')
    parser.add_argument('--workers', type=int, help='工作进程数 (仅生产模式)')
    parser.add_argument('--log-level', choices=['DEBUG', 'INFO', 'WARNING', 'ERROR'], 
                       default='INFO', help='日志级别 (默认: INFO)')
    parser.add_argument('--log-file', help='日志文件路径')
    
    args = parser.parse_args()
    
    # 设置日志
    setup_logging(args.log_level, args.log_file)
    
    logger.info("=" * 60)
    logger.info("OCR多线程服务启动器")
    logger.info("=" * 60)
    
    # 检查依赖项
    if not check_dependencies():
        sys.exit(1)
    
    # 创建必要目录
    create_directories()
    
    try:
        # 根据模式启动服务
        if args.mode == 'dev':
            start_development_server(args.host, args.port, args.debug)
        else:
            start_production_server(args.host, args.port, args.workers)
            
    except KeyboardInterrupt:
        logger.info("服务被用户中断")
    except Exception as e:
        logger.error(f"服务启动失败: {str(e)}")
        sys.exit(1)

if __name__ == "__main__":
    main()
